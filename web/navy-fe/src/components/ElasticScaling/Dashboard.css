.dashboard {
  padding: 0;
  background-color: #f0f2f5;
}

/* 页面头部样式 */
.page-header {
  background-color: #fff;
  border-radius: 4px;
  margin-bottom: 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.header-title {
  display: flex;
  align-items: center;
  padding: 18px 24px;
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  position: relative;
  letter-spacing: 0.5px;
}

.header-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: #f0f0f0;
}

.header-icon {
  font-size: 18px;
  margin-right: 10px;
  color: #1890ff;
  background: rgba(24, 144, 255, 0.1);
  padding: 8px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 统计卡片容器 */
.stats-cards {
  margin-bottom: 24px;
}

/* 统计卡片样式 */
.stat-card {
  background: #fff;
  padding: 24px;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  position: relative;
  overflow: hidden;
  margin-bottom: 16px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
  transform: translateY(-2px);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  background-color: #1890ff;
}

.stat-card.warning::before {
  background-color: #faad14;
}

.stat-card.success::before {
  background-color: #52c41a;
}

.stat-card.error::before {
  background-color: #f5222d;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 8px;
  line-height: 1.4;
}

.stat-label {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
  margin-bottom: 24px;
}

.stat-trend {
  display: flex;
  align-items: center;
  font-size: 13px;
}

.trend-up {
  color: #f5222d;
}

.trend-down {
  color: #52c41a;
}

/* 订单卡片样式 */
.order-card {
  border-radius: 4px;
  background-color: #fff;
  margin-bottom: 16px;
  border-left: 3px solid #1890ff;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  transition: all 0.3s;
  cursor: pointer;
}

.order-card.pool-in {
  border-left-color: #1890ff;
}

.order-card.pool-out {
  border-left-color: #faad14;
}

.order-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

.order-card-header {
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
}

.order-card-title {
  font-weight: 600;
  font-size: 16px;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.order-card-body {
  padding: 16px 24px;
}

.order-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
  width: 100%;
}

.order-meta-item {
  flex: 1;
  min-width: 120px;
}

.order-meta-label {
  color: rgba(0, 0, 0, 0.45);
  font-size: 13px;
  margin-bottom: 4px;
}

.order-meta-value {
  color: rgba(0, 0, 0, 0.88);
  font-weight: 600;
  font-size: 14px;
}

.order-card-footer {
  padding: 12px 24px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  background-color: #fafafa;
  border-top: 1px solid #f0f0f0;
}

/* 策略表格样式 */
.strategy-table .ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
}

/* 策略详情抽屉 */
.detail-drawer .ant-drawer-body {
  padding: 0;
  background-color: #f9fbfd;
}

.detail-drawer-header {
  padding: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.detail-drawer-content {
  padding: 24px;
  background-color: #f9fbfd;
}

.detail-section {
  margin-bottom: 24px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.detail-section .ant-descriptions {
  margin-bottom: 0;
}

.detail-section .ant-descriptions-bordered .ant-descriptions-item-label {
  background-color: #f5f7fa;
  font-weight: 500;
}

.detail-section-title {
  font-weight: 600;
  margin-bottom: 16px;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #f5f7fa;
  color: rgba(0, 0, 0, 0.85);
  display: flex;
  align-items: center;
}

.detail-section-title::before {
  content: '';
  display: inline-block;
  width: 4px;
  height: 16px;
  background-color: #1890ff;
  margin-right: 8px;
  border-radius: 2px;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 50px 0;
  width: 100%;
}

/* 空状态美化 */
.content-card {
  border-radius: 4px !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03) !important;
  margin-bottom: 20px;
}

.content-card .ant-card-head {
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  padding: 0 16px;
}

.content-card .ant-card-head-title {
  font-size: 15px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  letter-spacing: 0.3px;
  padding: 14px 0;
}

.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
  min-height: 220px;
  background-color: #fafafa;
  border-radius: 4px;
  text-align: center;
}

.empty-container .ant-empty-image {
  height: 100px;
  margin-bottom: 16px;
}

.empty-container .ant-empty-description {
  color: rgba(0, 0, 0, 0.45);
  font-size: 15px;
  line-height: 1.5;
  margin-bottom: 16px;
}

.empty-container .empty-action {
  margin-top: 16px;
}

.empty-container .empty-icon {
  font-size: 28px;
  color: #1890ff;
  margin-bottom: 16px;
  background: rgba(24, 144, 255, 0.1);
  padding: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 72px;
  height: 72px;
}

/* 设备列表样式 */
.device-list {
  margin-top: 8px;
}

.device-item {
  padding: 16px;
  border-radius: 8px;
  background-color: #fafafa;
  margin-bottom: 12px;
  border: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s;
}

.device-item:last-child {
  margin-bottom: 0;
}

.device-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
  transform: translateY(-2px);
  border-color: #e6f7ff;
  background-color: #f0f9ff;
}

.device-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.device-name {
  font-weight: 600;
  font-size: 15px;
  color: rgba(0, 0, 0, 0.85);
  display: flex;
  align-items: center;
  gap: 6px;
}

.device-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 6px;
  color: rgba(0, 0, 0, 0.65);
  font-size: 13px;
}

.device-meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: #f5f5f5;
  padding: 2px 8px;
  border-radius: 4px;
}

.device-meta-label {
  color: rgba(0, 0, 0, 0.45);
  font-weight: 500;
}

.device-status {
  padding: 2px 10px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
}

.status-special {
  background-color: #fff2e8;
  color: #fa541c;
  border: 1px solid #ffbb96;
}

.status-in-cluster {
  background-color: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.status-available {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

/* 订单标签页样式 */
.order-tabs .ant-tabs-nav {
  margin-bottom: 16px;
  background: #fafafa;
  padding: 8px 16px;
  border-radius: 8px;
}

.order-tabs .ant-tabs-tab {
  padding: 12px 16px;
  margin: 0 8px 0 0;
  transition: all 0.3s;
  border-radius: 4px;
}

.order-tabs .ant-tabs-tab:hover {
  background: #f0f0f0;
}

.order-tabs .ant-tabs-tab-active {
  background: white !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.order-tabs .ant-tabs-content {
  background: #fafafa;
  border-radius: 8px;
  padding: 16px;
}

.order-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  max-height: 400px;
  overflow-y: auto;
  padding-right: 8px;
}

.order-status-summary {
  display: flex;
  justify-content: space-around;
  padding: 16px;
  background: white;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.order-status-item {
  text-align: center;
}

.order-status-value {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 4px;
}

.order-status-label {
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
}

.order-status-pending {
  color: #f5222d;
}

.order-status-processing {
  color: #1890ff;
}

.order-status-done {
  color: #52c41a;
}

.order-status-ignored {
  color: #8c8c8c;
}

.order-count-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  height: 20px;
  padding: 0 6px;
  font-size: 12px;
  line-height: 20px;
  white-space: nowrap;
  background: #f5f5f5;
  border-radius: 10px;
  margin-left: 8px;
  transition: all 0.3s;
}

.order-count-badge.pending {
  background: #fff1f0;
  color: #f5222d;
}

.order-count-badge.processing {
  background: #e6f7ff;
  color: #1890ff;
}

.order-count-badge.done {
  background: #f6ffed;
  color: #52c41a;
}

.order-count-badge.all {
  background: #f5f5f5;
  color: #8c8c8c;
}

.order-count-badge.ignored {
  background: #f0f0f0;
  color: #8c8c8c;
}

.order-count-badge.custom {
  background: #fff7e6;
  color: #fa8c16;
}

/* 编辑策略模态框样式 */
.edit-strategy-modal .ant-modal-content {
  transition: all 0.3s ease-in-out;
  transform-origin: center top;
  animation: modalFadeIn 0.3s;
}

.edit-strategy-modal .ant-modal-body {
  transition: opacity 0.3s ease-in-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 资源使用信息卡片 */
.resource-info-card {
  margin-top: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  background-color: #fafafa;
}

.resource-info-card .resource-header {
  margin-bottom: 8px;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.resource-info-card .resource-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.resource-info-card .resource-item {
  margin-bottom: 12px;
}

.resource-info-card .resource-item-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.chart-container {
  width: 100%;
  height: 350px;
  position: relative;
}

/* 空图表样式 */
.empty-chart-container {
  width: 100%;
  height: 350px;
  background-color: #fafafa;
  border-radius: 8px;
  border: 1px dashed #e8e8e8;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.02);
}

.empty-chart-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(to right, transparent 0%, transparent 49.9%, #f0f0f0 50%, #f0f0f0 50.1%, transparent 51%, transparent 100%),
    linear-gradient(to bottom, transparent 0%, transparent 49.9%, #f0f0f0 50%, #f0f0f0 50.1%, transparent 51%, transparent 100%);
  background-size: 20px 20px;
  opacity: 0.3;
  z-index: 0;
}

.empty-chart-container .empty-chart-content {
  z-index: 1;
  text-align: center;
  padding: 24px 30px;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.empty-chart-container:hover .empty-chart-content {
  transform: translateY(-3px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.empty-chart-container .empty-chart-icon {
  font-size: 32px;
  color: #1890ff;
  margin-bottom: 16px;
  opacity: 0.7;
}

.empty-chart-container .empty-chart-title {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.65);
  margin-bottom: 8px;
}

.empty-chart-container .empty-chart-subtitle {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
}

/* 策略表单样式 */
.strategy-form .ant-form-item-label > label {
  color: rgba(0, 0, 0, 0.85);
}

.strategy-form .ant-input,
.strategy-form .ant-input-number,
.strategy-form .ant-select-selector {
  border-radius: 4px;
  border-color: #d9d9d9;
  transition: all 0.3s;
}

.strategy-form .ant-input:hover,
.strategy-form .ant-input-number:hover,
.strategy-form .ant-select-selector:hover {
  border-color: #40a9ff;
}

.strategy-form .ant-input:focus,
.strategy-form .ant-input-number-focused,
.strategy-form .ant-select-focused .ant-select-selector {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.strategy-form .ant-card {
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.strategy-form .ant-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

.strategy-form .ant-card-head {
  border-bottom: 1px solid #f0f0f0;
  padding: 0 16px;
  min-height: 40px;
  border-radius: 8px 8px 0 0;
}

.strategy-form .ant-card-head-title {
  padding: 10px 0;
}

.strategy-form .ant-select-selection-item {
  color: rgba(0, 0, 0, 0.85);
}

.strategy-form .ant-radio-button-wrapper {
  transition: all 0.3s;
}

.strategy-form .ant-radio-button-wrapper:first-child {
  border-radius: 4px 0 0 4px;
}

.strategy-form .ant-radio-button-wrapper:last-child {
  border-radius: 0 4px 4px 0;
}

.strategy-form .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
  background: #1890ff;
  border-color: #1890ff;
  color: #fff;
  box-shadow: -1px 0 0 0 #1890ff;
}

.strategy-form .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):hover {
  background: #40a9ff;
  border-color: #40a9ff;
  color: #fff;
}

.strategy-form .ant-form-item-has-error .ant-input,
.strategy-form .ant-form-item-has-error .ant-input-number,
.strategy-form .ant-form-item-has-error .ant-select-selector {
  border-color: #ff4d4f;
}

.strategy-form .ant-form-item-has-error .ant-input:focus,
.strategy-form .ant-form-item-has-error .ant-input-number-focused,
.strategy-form .ant-form-item-has-error .ant-select-focused .ant-select-selector {
  border-color: #ff4d4f;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

/* 统一模态框样式 */
.create-strategy-modal,
.edit-strategy-modal,
.create-order-modal,
.policy-modal {
  .ant-modal-header {
    padding: 16px 24px;
    border-bottom: 1px solid #f0f0f0;
    background: #ffffff;
    border-radius: 6px 6px 0 0;
    position: relative;

    .ant-modal-title {
      font-weight: 600;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      display: flex;
      align-items: center;
    }

    .ant-modal-close {
      position: absolute;
      top: 16px;
      right: 16px;
      z-index: 10;
      width: 22px;
      height: 22px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: none;
      background: transparent;
      transition: all 0.3s ease;

      &:hover {
        background-color: rgba(0, 0, 0, 0.06);
        border-radius: 4px;
      }

      .ant-modal-close-x {
        width: 22px;
        height: 22px;
        font-size: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: rgba(0, 0, 0, 0.45);
        transition: color 0.3s ease;

        &:hover {
          color: rgba(0, 0, 0, 0.75);
        }
      }
    }
  }

  .ant-modal-body {
    padding: 24px;
    background-color: #f9fbfd;
    max-height: 70vh;
    overflow-y: auto;
  }

  .ant-modal-footer {
    border-top: 1px solid #f0f0f0;
    padding: 16px 24px;
    background-color: #fff;
    border-radius: 0 0 6px 6px;
    text-align: right;

    .ant-btn {
      height: 40px;
      padding: 8px 20px;
      border-radius: 6px;
      font-weight: 500;
      font-size: 14px;
      margin-left: 12px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      &:first-child {
        margin-left: 0;
      }
    }

    .ant-btn-default {
      color: rgba(0, 0, 0, 0.65);
      border-color: #d9d9d9;
      background: #fff;

      &:hover {
        color: #40a9ff;
        border-color: #40a9ff;
        background: #f0f9ff;
      }
    }

    .ant-btn-primary {
      background: linear-gradient(135deg, #1890ff, #40a9ff);
      border: none;
      color: #fff;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);

      &:hover {
        background: linear-gradient(135deg, #40a9ff, #69c0ff);
        box-shadow: 0 4px 16px rgba(24, 144, 255, 0.4);
      }

      &:disabled {
        background: #f5f5f5;
        color: rgba(0, 0, 0, 0.25);
        box-shadow: none;
        transform: none;
      }
    }
  }

  .ant-form-item-label > label {
    font-weight: 500;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
  }

  .ant-card {
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    margin-bottom: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;

    &:hover {
      box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
      border-color: #e6f7ff;
    }

    .ant-card-head {
      background-color: #f5f7fa;
      min-height: 48px;
      padding: 0 20px;
      border-bottom: 1px solid #f0f0f0;

      .ant-card-head-title {
        padding: 12px 0;
        font-size: 14px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
      }
    }

    .ant-card-body {
      padding: 20px 24px;
      background: #fff;
    }
  }

  .ant-input,
  .ant-select-selector,
  .ant-input-number,
  .ant-input-affix-wrapper {
    border-radius: 6px;
    border-color: #d9d9d9;
    transition: all 0.3s ease;
    font-size: 14px;

    &:hover {
      border-color: #40a9ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
    }

    &:focus,
    &.ant-input-focused,
    &.ant-select-focused .ant-select-selector {
      border-color: #40a9ff;
      box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.2);
      outline: none;
    }
  }

  .ant-input-number-handler-wrap {
    border-radius: 0 6px 6px 0;
  }

  .ant-alert {
    border-radius: 6px;
    margin-bottom: 20px;
    border: 1px solid;

    &.ant-alert-info {
      background-color: #f0f9ff;
      border-color: #bae7ff;
    }
  }

  .ant-form-item {
    margin-bottom: 20px;
  }

  .ant-form-item-label {
    padding-bottom: 8px;
  }

  .ant-input {
    padding: 10px 12px;
    height: 40px;

    &::placeholder {
      color: rgba(0, 0, 0, 0.35);
    }
  }

  .ant-input-number {
    width: 100%;
    height: 40px;

    .ant-input-number-input {
      height: 38px;
      padding: 10px 12px;
    }
  }

  .ant-select {
    .ant-select-selector {
      height: 40px;
      padding: 6px 12px;

      .ant-select-selection-search-input {
        height: 28px;
      }

      .ant-select-selection-item {
        line-height: 28px;
        padding: 0;
      }

      .ant-select-selection-placeholder {
        line-height: 28px;
        color: rgba(0, 0, 0, 0.35);
      }
    }
  }

  .ant-select-multiple .ant-select-selector {
    padding: 4px 8px;
    min-height: 40px;

    .ant-select-selection-item {
      height: 28px;
      line-height: 26px;
      margin: 2px 4px 2px 0;
      padding: 0 8px;
      border-radius: 4px;
    }
  }

  .ant-radio-group {
    .ant-radio-button-wrapper {
      height: 40px;
      line-height: 38px;
      padding: 0 16px;
      border-radius: 0;
      transition: all 0.3s ease;

      &:first-child {
        border-radius: 6px 0 0 6px;
      }

      &:last-child {
        border-radius: 0 6px 6px 0;
      }

      &:hover {
        color: #40a9ff;
      }

      &.ant-radio-button-wrapper-checked {
        background: #1890ff;
        border-color: #1890ff;
        color: #fff;
        box-shadow: -1px 0 0 0 #1890ff;

        &:hover {
          background: #40a9ff;
          border-color: #40a9ff;
        }
      }
    }
  }

  .ant-checkbox-wrapper {
    font-size: 14px;

    .ant-checkbox {
      .ant-checkbox-inner {
        border-radius: 4px;
        transition: all 0.3s ease;
      }

      &.ant-checkbox-checked .ant-checkbox-inner {
        background-color: #1890ff;
        border-color: #1890ff;
      }
    }
  }
}