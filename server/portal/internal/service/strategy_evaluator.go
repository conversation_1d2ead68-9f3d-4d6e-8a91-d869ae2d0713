package service

import (
	"fmt"
	"navy-ng/models/portal"
	"sort"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// StrategyEvaluator 策略评估器
type StrategyEvaluator struct {
	service *ElasticScalingService
	logger  *zap.Logger
	db      *gorm.DB
}

// NewStrategyEvaluator 创建策略评估器
func NewStrategyEvaluator(service *ElasticScalingService) *StrategyEvaluator {
	return &StrategyEvaluator{
		service: service,
		logger:  service.logger,
		db:      service.db,
	}
}

// Evaluate 评估单个策略
func (e *StrategyEvaluator) Evaluate(strategy *portal.ElasticScalingStrategy) error {
	lockKey := fmt.Sprintf("elastic_scaling:strategy:%d:lock", strategy.ID)
	lockValue := fmt.Sprintf("evaluator_%d", time.Now().Unix())
	lockExpiry := 10 * time.Minute

	// 获取分布式锁
	isLocked, err := e.service.redisHandler.AcquireLock(lockKey, lockValue, lockExpiry)
	if err != nil {
		e.logger.Error("Failed to acquire lock for strategy evaluation",
			zap.Int64("strategyID", strategy.ID),
			zap.Error(err))
		return err
	}

	if !isLocked {
		e.logger.Info("Strategy is already being evaluated by another instance, skipping",
			zap.Int64("strategyID", strategy.ID))
		return nil
	}

	// 确保释放锁
	defer e.service.redisHandler.Delete(lockKey)

	// 检查冷却期
	if e.isInCooldown(strategy) {
		e.logger.Info("Strategy is in cooldown period, skipping evaluation",
			zap.Int64("strategyID", strategy.ID))
		return nil
	}

	// 获取关联集群
	clusters, err := e.getAssociatedClusters(strategy)
	if err != nil {
		e.logger.Error("Failed to get associated clusters",
			zap.Int64("strategyID", strategy.ID),
			zap.Error(err))
		return err
	}

	if len(clusters) == 0 {
		e.logger.Info("No associated clusters found for strategy",
			zap.Int64("strategyID", strategy.ID))
		return nil
	}

	// 解析资源类型
	resourceTypes := e.parseResourceTypes(strategy.ResourceType)

	// 评估每个集群和资源类型的组合
	for _, cluster := range clusters {
		for _, resourceType := range resourceTypes {
			if err := e.evaluateClusterResource(strategy, cluster.ID, resourceType); err != nil {
				e.logger.Error("Failed to evaluate cluster resource",
					zap.Int64("strategyID", strategy.ID),
					zap.Int64("clusterID", cluster.ID),
					zap.String("resourceType", resourceType),
					zap.Error(err))
			}
		}
	}

	return nil
}

// isInCooldown 检查策略是否在冷却期内
func (e *StrategyEvaluator) isInCooldown(strategy *portal.ElasticScalingStrategy) bool {
	if strategy.CooldownMinutes <= 0 {
		return false
	}

	cooldownDuration := time.Duration(strategy.CooldownMinutes) * time.Minute
	cutoffTime := time.Now().Add(-cooldownDuration)

	var count int64
	err := e.db.Model(&portal.StrategyExecutionHistory{}).
		Where("strategy_id = ? AND execution_time > ? AND result IN (?, ?)",
			strategy.ID, cutoffTime,
			StrategyExecutionResultOrderCreated,
			StrategyExecutionResultOrderCompleted).
		Count(&count).Error

	if err != nil {
		e.logger.Error("Failed to check cooldown period",
			zap.Int64("strategyID", strategy.ID),
			zap.Error(err))
		return false
	}

	return count > 0
}

// getAssociatedClusters 获取策略关联的集群
func (e *StrategyEvaluator) getAssociatedClusters(strategy *portal.ElasticScalingStrategy) ([]portal.K8sCluster, error) {
	var clusters []portal.K8sCluster
	err := e.db.Where("id IN (?)", strategy.ClusterIDs).Find(&clusters).Error
	return clusters, err
}

// parseResourceTypes 解析资源类型字符串
func (e *StrategyEvaluator) parseResourceTypes(resourceTypeStr string) []string {
	if resourceTypeStr == "" {
		return []string{"cpu", "memory"} // 默认值
	}
	return []string{resourceTypeStr}
}

// evaluateClusterResource 评估特定集群和资源类型
func (e *StrategyEvaluator) evaluateClusterResource(strategy *portal.ElasticScalingStrategy, clusterID int64, resourceType string) error {
	// 计算需要获取的天数
	durationDays := strategy.DurationMinutes / (24 * 60)
	if durationDays < 1 {
		durationDays = 1
	}

	// 获取资源快照
	snapshots, err := e.getResourceSnapshots(clusterID, resourceType, int(durationDays))
	if err != nil {
		return err
	}

	if len(snapshots) == 0 {
		e.logger.Info("No snapshots found for evaluation",
			zap.Int64("strategyID", strategy.ID),
			zap.Int64("clusterID", clusterID),
			zap.String("resourceType", resourceType))
		return nil
	}

	// 按天分组快照
	dailySnapshots := e.groupSnapshotsByDay(snapshots)

	// 计算连续超过阈值的天数
	consecutiveDays, cpuValues, memValues := e.calculateConsecutiveDays(strategy, dailySnapshots)

	// 判断是否触发策略
	if consecutiveDays >= int(durationDays) {
		e.logger.Info("Strategy threshold met, triggering device matching",
			zap.Int64("strategyID", strategy.ID),
			zap.Int64("clusterID", clusterID),
			zap.String("resourceType", resourceType),
			zap.Int("consecutiveDays", consecutiveDays))

		// 构建触发值和阈值字符串
		triggeredValueStr := e.buildTriggeredValueString(cpuValues, memValues, strategy)
		thresholdValueStr := e.buildThresholdString(strategy)

		// 调用设备匹配
		matcher := NewDeviceMatcher(e.service)
		return matcher.MatchDevices(strategy, clusterID, resourceType, triggeredValueStr, thresholdValueStr)
	} else {
		e.logger.Info("Strategy threshold not met",
			zap.Int64("strategyID", strategy.ID),
			zap.Int64("clusterID", clusterID),
			zap.String("resourceType", resourceType),
			zap.Int("consecutiveDays", consecutiveDays),
			zap.Int("requiredDays", int(durationDays)))
	}

	return nil
}

// getResourceSnapshots 获取资源快照
func (e *StrategyEvaluator) getResourceSnapshots(clusterID int64, resourceType string, days int) ([]portal.ResourceSnapshot, error) {
	startTime := time.Now().AddDate(0, 0, -days)
	var snapshots []portal.ResourceSnapshot

	err := e.db.Where("cluster_id = ? AND created_at >= ?", clusterID, startTime).
		Order("created_at DESC").
		Find(&snapshots).Error

	return snapshots, err
}

// groupSnapshotsByDay 按天分组快照
func (e *StrategyEvaluator) groupSnapshotsByDay(snapshots []portal.ResourceSnapshot) map[string][]portal.ResourceSnapshot {
	dailySnapshots := make(map[string][]portal.ResourceSnapshot)

	for _, snapshot := range snapshots {
		dayKey := time.Time(snapshot.CreatedAt).Format("2006-01-02")
		dailySnapshots[dayKey] = append(dailySnapshots[dayKey], snapshot)
	}

	// 对每天的快照按时间排序
	for day := range dailySnapshots {
		sort.Slice(dailySnapshots[day], func(i, j int) bool {
			return time.Time(dailySnapshots[day][i].CreatedAt).Before(time.Time(dailySnapshots[day][j].CreatedAt))
		})
	}

	return dailySnapshots
}

// calculateConsecutiveDays 计算连续超过阈值的天数
func (e *StrategyEvaluator) calculateConsecutiveDays(strategy *portal.ElasticScalingStrategy, dailySnapshots map[string][]portal.ResourceSnapshot) (int, []float64, []float64) {
	checker := NewThresholdChecker(strategy)
	consecutiveDays := 0
	maxConsecutiveDays := 0
	var allCpuValues []float64
	var allMemValues []float64

	// 获取所有日期并排序
	var dates []string
	for date := range dailySnapshots {
		dates = append(dates, date)
	}
	sort.Strings(dates)

	// 检查每一天
	for _, date := range dates {
		snapshots := dailySnapshots[date]
		breached, cpuValues, memValues := checker.CheckSnapshots(snapshots)

		if breached {
			consecutiveDays++
			allCpuValues = append(allCpuValues, cpuValues...)
			allMemValues = append(allMemValues, memValues...)
			if consecutiveDays > maxConsecutiveDays {
				maxConsecutiveDays = consecutiveDays
			}
		} else {
			consecutiveDays = 0 // 重置连续计数
		}
	}

	return maxConsecutiveDays, allCpuValues, allMemValues
}