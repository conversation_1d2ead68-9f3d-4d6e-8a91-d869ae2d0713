package service

import (
	"fmt"
	"navy-ng/models/portal"
	"sort"
	"strconv"
	"strings"
	"time"
)

// buildThresholdString 构建阈值字符串
func buildThresholdString(strategy *portal.ElasticScalingStrategy) string {
	var parts []string

	if strategy.CPUThresholdType != "" {
		parts = append(parts, fmt.Sprintf("CPU %s: %s%%", strategy.CPUThresholdType, strategy.CPUThreshold))
	}

	if strategy.MemoryThresholdType != "" {
		parts = append(parts, fmt.Sprintf("Memory %s: %s%%", strategy.MemoryThresholdType, strategy.MemoryThreshold))
	}

	if len(parts) > 0 {
		condition := "AND"
		if strategy.ThresholdCondition == "OR" {
			condition = "OR"
		}
		thresholdStr := strings.Join(parts, " "+condition+" ")

		// 添加触发动作和持续天数
		actionStr := "Scale Out"
		if strategy.ThresholdTriggerAction == TriggerActionPoolExit {
			actionStr = "Scale In"
		}

		durationDays := strategy.DurationMinutes / (24 * 60) // 转换为天数
		return fmt.Sprintf("%s (%s for %d days)", thresholdStr, actionStr, durationDays)
	}

	return "No threshold defined"
}

// buildTriggeredValueString 构建触发值字符串
func buildTriggeredValueString(snapshots []*portal.ResourceSnapshot, strategy *portal.ElasticScalingStrategy) string {
	if len(snapshots) == 0 {
		return "No data"
	}

	// 按创建时间排序
	sort.Slice(snapshots, func(i, j int) bool {
		return snapshots[i].CreatedAt.Before(time.Time(snapshots[j].CreatedAt))
	})

	var parts []string

	// 获取最新的快照数据
	latestSnapshot := snapshots[len(snapshots)-1]

	if strategy.CPUThresholdType != "" {
		var cpuValue string
		if strategy.CPUThresholdType == "utilization" {
			cpuValue = fmt.Sprintf("%.2f%%", latestSnapshot.CPUUtilization)
		} else {
			cpuValue = fmt.Sprintf("%.2f%%", latestSnapshot.CPUAllocation)
		}
		parts = append(parts, fmt.Sprintf("CPU %s: %s", strategy.CPUThresholdType, cpuValue))
	}

	if strategy.MemoryThresholdType != "" {
		var memValue string
		if strategy.MemoryThresholdType == "utilization" {
			memValue = fmt.Sprintf("%.2f%%", latestSnapshot.MemoryUtilization)
		} else {
			memValue = fmt.Sprintf("%.2f%%", latestSnapshot.MemoryAllocation)
		}
		parts = append(parts, fmt.Sprintf("Memory %s: %s", strategy.MemoryThresholdType, memValue))
	}

	if len(parts) > 0 {
		return strings.Join(parts, ", ")
	}

	return "No data"
}

// parseThreshold 解析阈值字符串为浮点数
func parseThreshold(thresholdStr string) (float64, error) {
	return strconv.ParseFloat(thresholdStr, 64)
}

// generateLockKey 生成Redis锁的键
func generateLockKey(strategyID int64) string {
	return fmt.Sprintf("elastic_scaling_strategy_%d", strategyID)
}

// parseResourceTypes 解析资源类型字符串
func parseResourceTypes(resourceTypesStr string) []string {
	if resourceTypesStr == "" {
		return []string{"cpu", "memory"} // 默认值
	}

	types := strings.Split(resourceTypesStr, ",")
	var result []string
	for _, t := range types {
		t = strings.TrimSpace(t)
		if t != "" {
			result = append(result, t)
		}
	}

	if len(result) == 0 {
		return []string{"cpu", "memory"} // 默认值
	}

	return result
}

// groupSnapshotsByDay 按天分组快照
func groupSnapshotsByDay(snapshots []*portal.ResourceSnapshot) map[string][]*portal.ResourceSnapshot {
	grouped := make(map[string][]*portal.ResourceSnapshot)

	for _, snapshot := range snapshots {
		dateKey := time.Time(snapshot.CreatedAt).Format("2006-01-02")
		grouped[dateKey] = append(grouped[dateKey], snapshot)
	}

	return grouped
}

// getSortedDates 获取排序后的日期列表
func getSortedDates(grouped map[string][]*portal.ResourceSnapshot) []string {
	var dates []string
	for date := range grouped {
		dates = append(dates, date)
	}
	sort.Strings(dates)
	return dates
}