package service

import (
	"fmt"
	"navy-ng/models/portal"
	"time"

	"go.uber.org/zap"
)

// OrderGenerator 订单生成器
type OrderGenerator struct {
	service *ElasticScalingService
	logger  *zap.Logger
}

// NewOrderGenerator 创建订单生成器
func NewOrderGenerator(service *ElasticScalingService) *OrderGenerator {
	return &OrderGenerator{
		service: service,
		logger:  service.logger,
	}
}

// GenerateOrder 生成弹性伸缩订单
func (g *OrderGenerator) GenerateOrder(
	strategy *portal.ElasticScalingStrategy,
	clusterID int64,
	selectedDeviceIDs []int64,
	triggeredValueStr string,
	thresholdValueStr string,
) error {
	g.logger.Info("Generating elastic scaling order",
		zap.Int64("strategyID", strategy.ID),
		zap.Int64("clusterID", clusterID),
		zap.String("actionType", strategy.ThresholdTriggerAction),
		zap.Int("deviceCount", len(selectedDeviceIDs)))

	orderDTO := OrderDTO{
		ClusterID:              clusterID,
		StrategyID:             &strategy.ID,
		ActionType:             strategy.ThresholdTriggerAction,
		DeviceCount:            len(selectedDeviceIDs),
		Devices:                selectedDeviceIDs,
		StrategyTriggeredValue: triggeredValueStr,
		StrategyThresholdValue: thresholdValueStr,
		CreatedBy:              SystemAutoCreator,
		// Status will be set by CreateOrder, typically to "pending"
	}

	orderID, err := g.service.CreateOrder(orderDTO)
	currentTime := portal.NavyTime(time.Now())

	if err != nil {
		g.logger.Error("Failed to create elastic scaling order",
			zap.Int64("strategyID", strategy.ID),
			zap.Int64("clusterID", clusterID),
			zap.Error(err))

		reason := fmt.Sprintf("Failed to create order for cluster %d: %v", clusterID, err)
		g.recordStrategyExecution(strategy.ID, StrategyExecutionResultOrderFailed, nil, reason, triggeredValueStr, thresholdValueStr, &currentTime)
		return err
	}

	g.logger.Info("Successfully created elastic scaling order",
		zap.Int64("orderID", orderID),
		zap.Int64("strategyID", strategy.ID),
		zap.Int64("clusterID", clusterID))

	reason := fmt.Sprintf("Successfully created order %d for cluster %d", orderID, clusterID)
	g.recordStrategyExecution(strategy.ID, StrategyExecutionResultOrderCreated, &orderID, reason, triggeredValueStr, thresholdValueStr, &currentTime)

	// TODO: 根据设计文档，需要查询当周值班人员并向其发送运维通知
	g.logger.Info("Placeholder: Trigger notification to duty roster about the new order.", zap.Int64("orderID", orderID))

	return nil
}

// recordStrategyExecution 记录策略执行历史
func (g *OrderGenerator) recordStrategyExecution(
	strategyID int64,
	result string,
	orderID *int64,
	reason string,
	triggeredValue string,
	thresholdValue string,
	specificExecutionTime *portal.NavyTime,
) error {
	return g.service.recordStrategyExecution(strategyID, result, orderID, reason, triggeredValue, thresholdValue, specificExecutionTime)
}