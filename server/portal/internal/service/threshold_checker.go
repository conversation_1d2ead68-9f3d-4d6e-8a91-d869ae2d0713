package service

import (
	"navy-ng/models/portal"
)

// ThresholdChecker 阈值检查器
type ThresholdChecker struct {
	strategy *portal.ElasticScalingStrategy
}

// NewThresholdChecker 创建阈值检查器
func NewThresholdChecker(strategy *portal.ElasticScalingStrategy) *ThresholdChecker {
	return &ThresholdChecker{
		strategy: strategy,
	}
}

// CheckSnapshots 检查快照是否超过阈值
func (c *ThresholdChecker) CheckSnapshots(snapshots []portal.ResourceSnapshot) (bool, []float64, []float64) {
	var cpuValues []float64
	var memValues []float64

	for _, snapshot := range snapshots {
		if c.checkSingleSnapshot(snapshot) {
			if c.strategy.CPUThresholdValue > 0 {
				if c.strategy.CPUThresholdType == "usage" {
					cpuValues = append(cpuValues, snapshot.CPUUsagePercent)
				} else if c.strategy.CPUThresholdType == "allocation" {
					cpuValues = append(cpuValues, snapshot.CPUAllocationPercent)
				}
			}
			if c.strategy.MemoryThresholdValue > 0 {
				if c.strategy.MemoryThresholdType == "usage" {
					memValues = append(memValues, snapshot.MemoryUsagePercent)
				} else if c.strategy.MemoryThresholdType == "allocation" {
					memValues = append(memValues, snapshot.MemoryAllocationPercent)
				}
			}
			return true, cpuValues, memValues
		}
	}

	return false, cpuValues, memValues
}

// checkSingleSnapshot 检查单个快照是否超过阈值
func (c *ThresholdChecker) checkSingleSnapshot(snapshot portal.ResourceSnapshot) bool {
	cpuBreached := false
	memBreached := false

	// Check CPU threshold
	if c.strategy.CPUThresholdValue > 0 {
		var cpuValue float64
		if c.strategy.CPUThresholdType == "usage" {
			cpuValue = snapshot.CPUUsagePercent
		} else if c.strategy.CPUThresholdType == "allocation" {
			cpuValue = snapshot.CPUAllocationPercent
		}

		if c.strategy.ThresholdTriggerAction == TriggerActionPoolEntry {
			cpuBreached = cpuValue > c.strategy.CPUThresholdValue
		} else if c.strategy.ThresholdTriggerAction == TriggerActionPoolExit {
			cpuBreached = cpuValue < c.strategy.CPUThresholdValue
		}
	} else {
		cpuBreached = true // If no CPU threshold is set, consider it as not breached (neutral)
	}

	// Check Memory threshold
	if c.strategy.MemoryThresholdValue > 0 {
		var memValue float64
		if c.strategy.MemoryThresholdType == "usage" {
			memValue = snapshot.MemoryUsagePercent
		} else if c.strategy.MemoryThresholdType == "allocation" {
			memValue = snapshot.MemoryAllocationPercent
		}

		if c.strategy.ThresholdTriggerAction == TriggerActionPoolEntry {
			memBreached = memValue > c.strategy.MemoryThresholdValue
		} else if c.strategy.ThresholdTriggerAction == TriggerActionPoolExit {
			memBreached = memValue < c.strategy.MemoryThresholdValue
		}
	} else {
		memBreached = true // If no Memory threshold is set, consider it as not breached (neutral)
	}

	// Apply condition logic
	if c.strategy.ConditionLogic == "AND" {
		return cpuBreached && memBreached
	} else if c.strategy.ConditionLogic == "OR" {
		return cpuBreached || memBreached
	}

	// Default to AND logic if not specified
	return cpuBreached && memBreached
}