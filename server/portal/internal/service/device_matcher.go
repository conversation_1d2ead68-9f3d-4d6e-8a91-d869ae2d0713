package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"navy-ng/models/portal"
	"navy-ng/pkg/redis"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// DeviceMatcher 设备匹配器
type DeviceMatcher struct {
	service *ElasticScalingService
	logger  *zap.Logger
	db      *gorm.DB
}

// NewDeviceMatcher 创建设备匹配器
func NewDeviceMatcher(service *ElasticScalingService) *DeviceMatcher {
	return &DeviceMatcher{
		service: service,
		logger:  service.logger,
		db:      service.db,
	}
}

// MatchDevices 为策略匹配合适的设备
func (m *DeviceMatcher) MatchDevices(
	strategy *portal.ElasticScalingStrategy,
	clusterID int64,
	resourceType string,
	triggeredValueStr string,
	thresholdValueStr string,
) error {
	m.logger.Info("Starting device matching for strategy",
		zap.Int64("strategyID", strategy.ID),
		zap.Int64("clusterID", clusterID),
		zap.String("resourceType", resourceType),
		zap.String("action", strategy.ThresholdTriggerAction))

	currentTime := portal.NavyTime(time.Now())

	// 获取查询模板ID
	queryTemplateID, err := m.getQueryTemplateID(strategy)
	if err != nil {
		m.recordStrategyExecution(strategy.ID, StrategyExecutionResultFailureInvalidTemplateID, nil, err.Error(), triggeredValueStr, thresholdValueStr, &currentTime)
		return err
	}

	m.logger.Info("Using query template for device matching",
		zap.Int64("templateID", queryTemplateID),
		zap.Int64("strategyID", strategy.ID))

	// 获取查询模板
	queryTemplate, err := m.getQueryTemplate(queryTemplateID)
	if err != nil {
		result := StrategyExecutionResultFailureTemplateNotFound
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			result = StrategyExecutionResultFailureDBError
		}
		m.recordStrategyExecution(strategy.ID, result, nil, err.Error(), triggeredValueStr, thresholdValueStr, &currentTime)
		return err
	}

	// 解析过滤组
	filterGroups, err := m.parseFilterGroups(queryTemplate.Groups)
	if err != nil {
		m.recordStrategyExecution(strategy.ID, StrategyExecutionResultFailureTemplateUnmarshal, nil, err.Error(), triggeredValueStr, thresholdValueStr, &currentTime)
		return err
	}

	// 查询候选设备
	candidateDevices, err := m.queryCandidateDevices(filterGroups, strategy.ID)
	if err != nil {
		m.recordStrategyExecution(strategy.ID, StrategyExecutionResultFailureDeviceQuery, nil, err.Error(), triggeredValueStr, thresholdValueStr, &currentTime)
		return err
	}

	if len(candidateDevices) == 0 {
		reason := fmt.Sprintf("No candidate devices found for cluster %d, resource type %s using template %d", clusterID, resourceType, queryTemplateID)
		m.logger.Info(reason, zap.Int64("strategyID", strategy.ID))
		m.recordStrategyExecution(strategy.ID, StrategyExecutionResultFailureNoDevicesFound, nil, reason, triggeredValueStr, thresholdValueStr, &currentTime)
		return nil
	}

	m.logger.Info("Successfully queried candidate devices",
		zap.Int64("strategyID", strategy.ID),
		zap.Int64("templateID", queryTemplateID),
		zap.Int("candidateCount", len(candidateDevices)))

	// 筛选合适的设备
	suitableDevices := m.filterSuitableDevices(candidateDevices, strategy, clusterID)
	if len(suitableDevices) == 0 {
		reason := fmt.Sprintf("No suitable devices selected after filtering for action %s on cluster %d, template %d. Candidates from query: %d.",
			strategy.ThresholdTriggerAction, clusterID, queryTemplateID, len(candidateDevices))
		m.logger.Info(reason, zap.Int64("strategyID", strategy.ID))
		m.recordStrategyExecution(strategy.ID, StrategyExecutionResultFailureNoSuitableDevices, nil, reason, triggeredValueStr, thresholdValueStr, &currentTime)
		return nil
	}

	// 选择指定数量的设备
	selectedDeviceIDs := m.selectDevices(suitableDevices, strategy.DeviceCount)

	m.logger.Info("Selected devices for strategy action",
		zap.Int64("strategyID", strategy.ID),
		zap.Int64s("selectedDeviceIDs", selectedDeviceIDs),
		zap.Int("numDevicesToChange", int(strategy.DeviceCount)),
		zap.Int("suitableCandidateCount", len(suitableDevices)))

	if len(selectedDeviceIDs) > 0 {
		m.logger.Info("Selected devices, proceeding to generate elastic scaling order",
			zap.Int64("strategyID", strategy.ID),
			zap.Int64s("selectedDeviceIDs", selectedDeviceIDs))

		// 生成弹性伸缩订单
		generator := NewOrderGenerator(m.service)
		err := generator.GenerateOrder(strategy, clusterID, selectedDeviceIDs, triggeredValueStr, thresholdValueStr)
		if err != nil {
			m.logger.Error("Failed to generate elastic scaling order",
				zap.Int64("strategyID", strategy.ID),
				zap.Error(err))
		}
	} else {
		reason := fmt.Sprintf("No devices were ultimately selected for order generation for strategy %d on cluster %d.", strategy.ID, clusterID)
		m.logger.Warn(reason, zap.Int64("strategyID", strategy.ID))
		m.recordStrategyExecution(strategy.ID, StrategyExecutionResultFailureNoDevicesForOrder, nil, reason, triggeredValueStr, thresholdValueStr, &currentTime)
	}

	return nil
}

// getQueryTemplateID 获取查询模板ID
func (m *DeviceMatcher) getQueryTemplateID(strategy *portal.ElasticScalingStrategy) (int64, error) {
	var queryTemplateID int64
	if strategy.ThresholdTriggerAction == TriggerActionPoolEntry {
		queryTemplateID = strategy.EntryQueryTemplateID
	} else if strategy.ThresholdTriggerAction == TriggerActionPoolExit {
		queryTemplateID = strategy.ExitQueryTemplateID
	}

	if queryTemplateID == 0 {
		return 0, fmt.Errorf("Query template ID is not set for action type %s on strategy ID %d", strategy.ThresholdTriggerAction, strategy.ID)
	}

	return queryTemplateID, nil
}

// getQueryTemplate 获取查询模板
func (m *DeviceMatcher) getQueryTemplate(templateID int64) (*portal.QueryTemplate, error) {
	var queryTemplate portal.QueryTemplate
	err := m.db.First(&queryTemplate, templateID).Error
	if err != nil {
		return nil, fmt.Errorf("Failed to find query template ID %d: %v", templateID, err)
	}
	return &queryTemplate, nil
}

// parseFilterGroups 解析过滤组
func (m *DeviceMatcher) parseFilterGroups(groupsJSON string) ([]FilterGroup, error) {
	var filterGroups []FilterGroup
	err := json.Unmarshal([]byte(groupsJSON), &filterGroups)
	if err != nil {
		return nil, fmt.Errorf("Failed to unmarshal filter groups: %v", err)
	}
	return filterGroups, nil
}

// queryCandidateDevices 查询候选设备
func (m *DeviceMatcher) queryCandidateDevices(filterGroups []FilterGroup, strategyID int64) ([]DeviceResponse, error) {
	// 创建设备查询服务
	deviceCache, ok := m.service.cache.(*DeviceCache)
	if !ok {
		// 如果类型断言失败，创建一个新的设备缓存
		deviceCache = NewDeviceCache(m.service.redisHandler.(*redis.Handler), redis.NewKeyBuilder("navy", "v1"))
	}
	deviceQuerySvc := NewDeviceQueryService(m.db, deviceCache)

	deviceRequest := &DeviceQueryRequest{
		Groups: filterGroups,
		Page:   1,
		Size:   1000, // 获取大量候选设备
	}

	m.logger.Info("Querying candidate devices using template",
		zap.Int64("strategyID", strategyID),
		zap.Any("requestGroups", deviceRequest.Groups))

	candidateDevicesResponse, err := deviceQuerySvc.QueryDevices(context.Background(), deviceRequest)
	if err != nil {
		return nil, fmt.Errorf("Error querying devices: %v", err)
	}

	if candidateDevicesResponse == nil {
		return []DeviceResponse{}, nil
	}

	return candidateDevicesResponse.List, nil
}

// filterSuitableDevices 筛选合适的设备
func (m *DeviceMatcher) filterSuitableDevices(candidates []DeviceResponse, strategy *portal.ElasticScalingStrategy, clusterID int64) []DeviceResponse {
	var suitableDevices []DeviceResponse

	if strategy.ThresholdTriggerAction == TriggerActionPoolEntry {
		// 优先选择未分配到任何集群的设备
		var unassignedDevices []DeviceResponse
		var assignedDevices []DeviceResponse

		for _, device := range candidates {
			if device.ClusterID == 0 || device.Cluster == "" {
				unassignedDevices = append(unassignedDevices, device)
			} else {
				assignedDevices = append(assignedDevices, device)
			}
		}

		// 先添加未分配的设备，然后添加已分配的设备
		suitableDevices = append(suitableDevices, unassignedDevices...)
		suitableDevices = append(suitableDevices, assignedDevices...)

	} else if strategy.ThresholdTriggerAction == TriggerActionPoolExit {
		// 只选择属于当前集群的设备
		for _, device := range candidates {
			if int64(device.ClusterID) == clusterID {
				suitableDevices = append(suitableDevices, device)
			}
		}
	}

	return suitableDevices
}

// selectDevices 选择指定数量的设备
func (m *DeviceMatcher) selectDevices(suitableDevices []DeviceResponse, deviceCount int32) []int64 {
	numDevicesToChange := int(deviceCount)
	if numDevicesToChange <= 0 {
		numDevicesToChange = 1
		m.logger.Warn("Strategy DeviceCount is not positive, defaulting to 1",
			zap.Int("originalDeviceCount", int(deviceCount)))
	}

	var selectedDeviceIDs []int64
	for i := 0; i < len(suitableDevices) && len(selectedDeviceIDs) < numDevicesToChange; i++ {
		selectedDeviceIDs = append(selectedDeviceIDs, suitableDevices[i].ID)
	}

	return selectedDeviceIDs
}

// recordStrategyExecution 记录策略执行历史
func (m *DeviceMatcher) recordStrategyExecution(
	strategyID int64,
	result string,
	orderID *int64,
	reason string,
	triggeredValue string,
	thresholdValue string,
	specificExecutionTime *portal.NavyTime,
) error {
	return m.service.recordStrategyExecution(strategyID, result, orderID, reason, triggeredValue, thresholdValue, specificExecutionTime)
}